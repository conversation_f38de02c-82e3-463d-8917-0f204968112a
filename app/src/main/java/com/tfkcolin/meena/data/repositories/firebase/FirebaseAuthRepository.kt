package com.tfkcolin.meena.data.repositories.firebase

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import com.tfkcolin.meena.data.local.UserDao
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.data.models.firebase.FirebaseUserProfile
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase implementation of IAuthRepository.
 * Uses Firebase Authentication for user authentication and Firestore for user data storage.
 */
@Singleton
class FirebaseAuthRepository @Inject constructor(
    private val firebaseAuth: FirebaseAuth,
    private val firestore: FirebaseFirestore,
    private val userDao: UserDao,
    private val tokenManager: TokenManager
) : IAuthRepository {

    companion object {
        private const val USERS_COLLECTION = "users"
    }

    override suspend fun register(request: RegisterRequest): Result<AuthResponse> {
        return try {
            // Create user with email and password
            val authResult = firebaseAuth.createUserWithEmailAndPassword(
                request.email, 
                request.password
            ).await()

            val firebaseUser = authResult.user ?: throw Exception("Failed to create user")

            // Create user profile in Firestore
            val userProfile = createUserProfile(firebaseUser, request)
            
            // Save user to local database
            saveUserToLocal(userProfile, request)

            // Save tokens and user info
            val accessToken = firebaseUser.getIdToken(false).await().token ?: ""
            tokenManager.saveAccessToken(accessToken)
            tokenManager.saveUserId(firebaseUser.uid)
            tokenManager.saveUserHandle(request.userHandle)

            // Create auth response
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = firebaseUser.uid,
                    userHandle = request.userHandle,
                    displayName = request.displayName ?: request.userHandle,
                    bio = "",
                    avatarUrl = "",
                    verificationStatus = "none",
                    isVerified = false,
                    isGoldMember = false,
                    lastActive = System.currentTimeMillis().toString(),
                    createdAt = System.currentTimeMillis().toString(),
                    followerCount = 0,
                    followingCount = 0
                ),
                accessToken = accessToken,
                refreshToken = "", // Firebase handles refresh tokens internally
                expiresIn = 3600, // 1 hour
                recoveryPhrase = null
            )

            Result.success(authResponse)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun login(request: LoginRequest): Result<AuthResponse> {
        return try {
            // Sign in with email and password
            val authResult = firebaseAuth.signInWithEmailAndPassword(
                request.identifier, // Assuming identifier is email for Firebase
                request.password
            ).await()

            val firebaseUser = authResult.user ?: throw Exception("Failed to sign in")

            // Get user profile from Firestore
            val userDoc = firestore.collection(USERS_COLLECTION)
                .document(firebaseUser.uid)
                .get()
                .await()

            if (!userDoc.exists()) {
                throw Exception("User profile not found")
            }

            val userProfile = userDoc.toObject(FirebaseUserProfile::class.java)
                ?: throw Exception("Failed to parse user profile")

            // Get access token
            val accessToken = firebaseUser.getIdToken(false).await().token ?: ""
            tokenManager.saveAccessToken(accessToken)
            tokenManager.saveUserId(firebaseUser.uid)
            tokenManager.saveUserHandle(userProfile.userHandle)

            // Create auth response
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = firebaseUser.uid,
                    userHandle = userProfile.userHandle,
                    displayName = userProfile.displayName,
                    bio = userProfile.bio,
                    avatarUrl = userProfile.avatarUrl,
                    verificationStatus = userProfile.verificationStatus,
                    isVerified = userProfile.verificationStatus == "verified",
                    isGoldMember = userProfile.subscriptionTier == "gold",
                    lastActive = userProfile.lastActive.toString(),
                    createdAt = userProfile.createdAt.toString(),
                    followerCount = 0,
                    followingCount = 0
                ),
                accessToken = accessToken,
                refreshToken = "",
                expiresIn = 3600
            )

            Result.success(authResponse)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun twoFactorAuth(request: TwoFactorAuthRequest): Result<AuthResponse> {
        // Firebase handles 2FA differently, this is a placeholder implementation
        return Result.failure(Exception("2FA not implemented for Firebase"))
    }

    override suspend fun refreshToken(): Result<AuthResponse> {
        return try {
            val currentUser = firebaseAuth.currentUser ?: throw Exception("No authenticated user")
            
            // Force token refresh
            val accessToken = currentUser.getIdToken(true).await().token ?: ""
            tokenManager.saveAccessToken(accessToken)

            // Get user profile from Firestore
            val userDoc = firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .get()
                .await()

            val userProfile = userDoc.toObject(FirebaseUserProfile::class.java)
                ?: throw Exception("Failed to parse user profile")

            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = currentUser.uid,
                    userHandle = userProfile.userHandle,
                    displayName = userProfile.displayName,
                    bio = userProfile.bio,
                    avatarUrl = userProfile.avatarUrl,
                    verificationStatus = userProfile.verificationStatus,
                    isVerified = userProfile.verificationStatus == "verified",
                    isGoldMember = userProfile.subscriptionTier == "gold",
                    lastActive = userProfile.lastActive.toString(),
                    createdAt = userProfile.createdAt.toString(),
                    followerCount = 0,
                    followingCount = 0
                ),
                accessToken = accessToken,
                refreshToken = "",
                expiresIn = 3600
            )

            Result.success(authResponse)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun recoverAccount(request: AccountRecoveryRequest): Result<AuthResponse> {
        // Firebase doesn't have built-in recovery phrase functionality
        // This would need to be implemented with custom logic
        return Result.failure(Exception("Account recovery not implemented for Firebase"))
    }

    override suspend fun changePassword(request: PasswordChangeRequest): Result<Unit> {
        return try {
            val currentUser = firebaseAuth.currentUser ?: throw Exception("No authenticated user")
            currentUser.updatePassword(request.newPassword).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getCurrentUser(): Flow<User?> {
        val userId = tokenManager.getUserId()
        return if (userId != null) {
            userDao.getCurrentUserFlow(userId)
        } else {
            flow { emit(null) }
        }
    }

    override fun isLoggedIn(): Boolean {
        return tokenManager.isLoggedIn() && firebaseAuth.currentUser != null
    }

    override suspend fun logout() {
        try {
            firebaseAuth.signOut()
            tokenManager.clearTokens()

            // Clear user data
            val userId = tokenManager.getUserId()
            if (userId != null) {
                userDao.deleteUser(userId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun deleteAccount(): Result<Unit> {
        return try {
            val currentUser = firebaseAuth.currentUser ?: throw Exception("No authenticated user")
            
            // Delete user profile from Firestore
            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .delete()
                .await()

            // Delete Firebase Auth account
            currentUser.delete().await()
            
            tokenManager.clearTokens()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private suspend fun createUserProfile(firebaseUser: FirebaseUser, request: RegisterRequest): FirebaseUserProfile {
        val userProfile = FirebaseUserProfile(
            userId = firebaseUser.uid,
            userHandle = request.userHandle,
            email = request.email,
            phoneNumber = request.phoneNumber ?: "",
            displayName = request.displayName ?: request.userHandle,
            bio = "",
            avatarUrl = "",
            subscriptionTier = "free",
            verificationStatus = "none",
            isActive = true,
            createdAt = System.currentTimeMillis(),
            lastActive = System.currentTimeMillis()
        )

        // Save to Firestore
        firestore.collection(USERS_COLLECTION)
            .document(firebaseUser.uid)
            .set(userProfile)
            .await()

        return userProfile
    }

    private suspend fun saveUserToLocal(userProfile: FirebaseUserProfile, request: RegisterRequest) {
        val user = User(
            userId = userProfile.userId,
            userHandle = userProfile.userHandle,
            displayName = userProfile.displayName,
            profilePictureUrl = userProfile.avatarUrl,
            bio = userProfile.bio,
            phoneNumber = request.phoneNumber ?: "",
            email = request.email,
            subscriptionTier = userProfile.subscriptionTier,
            verificationStatus = userProfile.verificationStatus,
            isActive = userProfile.isActive,
            createdAt = userProfile.createdAt.toString(),
            lastSeenAt = userProfile.lastActive.toString()
        )

        userDao.insertUser(user)
    }
}
