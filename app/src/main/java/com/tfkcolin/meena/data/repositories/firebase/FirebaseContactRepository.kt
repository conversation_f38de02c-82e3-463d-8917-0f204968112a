package com.tfkcolin.meena.data.repositories.firebase

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import com.tfkcolin.meena.data.local.ContactDao
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.data.models.firebase.FirebaseContact
import com.tfkcolin.meena.domain.repositories.IContactRepository
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase implementation of IContactRepository.
 * Uses Firestore for contact data storage and real-time updates.
 */
@Singleton
class FirebaseContactRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val contactDao: ContactDao,
    private val tokenManager: TokenManager
) : IContactRepository {

    companion object {
        private const val CONTACTS_COLLECTION = "contacts"
        private const val USERS_COLLECTION = "users"
    }

    override suspend fun getContacts(
        limit: Int,
        offset: Int,
        relationship: String?
    ): Result<ContactListResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            var query = firestore.collection(CONTACTS_COLLECTION)
                .whereEqualTo("userId", currentUserId)

            // Apply relationship filter if provided
            relationship?.let {
                query = query.whereEqualTo("relationship", it)
            }

            val querySnapshot = query
                .limit(limit.toLong())
                .get()
                .await()

            val firebaseContacts = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseContact::class.java)
            }

            // Convert to Contact models and get user profiles
            val contacts = firebaseContacts.map { firebaseContact ->
                val userProfile = getUserProfile(firebaseContact.contactId)
                firebaseContact.toContact(userProfile)
            }

            // Update local database
            contacts.forEach { contact ->
                try {
                    contactDao.insertContact(contact)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            val response = ContactListResponse(
                contacts = contacts,
                totalCount = contacts.size,
                hasMore = contacts.size == limit
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getContactsFlow(): Flow<List<Contact>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(CONTACTS_COLLECTION)
                .whereEqualTo("userId", currentUserId)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val firebaseContacts = snapshot.documents.mapNotNull { doc ->
                            doc.toObject(FirebaseContact::class.java)
                        }

                        // Convert to Contact models (without user profiles for performance)
                        val contacts = firebaseContacts.map { firebaseContact ->
                            firebaseContact.toContact(null)
                        }

                        // Update local database
                        contacts.forEach { contact ->
                            try {
                                contactDao.insertContact(contact)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }

                        trySend(contacts)
                    }
                }
        } else {
            trySend(emptyList())
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    override suspend fun addContact(
        contactId: String,
        displayName: String?,
        notes: String?
    ): Result<com.tfkcolin.meena.data.models.ContactResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            val contactDocId = "${currentUserId}_${contactId}"
            val firebaseContact = FirebaseContact(
                id = contactDocId,
                userId = currentUserId,
                contactId = contactId,
                displayName = displayName,
                relationship = "pending",
                notes = notes,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            // Add to Firestore
            firestore.collection(CONTACTS_COLLECTION)
                .document(contactDocId)
                .set(firebaseContact)
                .await()

            // Get user profile and convert to Contact
            val userProfile = getUserProfile(contactId)
            val contact = firebaseContact.toContact(userProfile)

            // Update local database
            contactDao.insertContact(contact)

            val response = com.tfkcolin.meena.data.models.ContactResponse(
                contact.id, contact.userId, contact.contactId, contact.displayName,
                contact.relationship, contact.notes, contact.createdAt, contact.updatedAt,
                contact.user
            )
            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateContact(
        contactId: String,
        displayName: String?,
        relationship: String?,
        notes: String?
    ): Result<com.tfkcolin.meena.data.models.ContactResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            val contactDocId = "${currentUserId}_${contactId}"
            val updates = mutableMapOf<String, Any>()
            
            displayName?.let { updates["displayName"] = it }
            relationship?.let { updates["relationship"] = it }
            notes?.let { updates["notes"] = it }
            updates["updatedAt"] = System.currentTimeMillis()

            // Update in Firestore
            firestore.collection(CONTACTS_COLLECTION)
                .document(contactDocId)
                .update(updates)
                .await()

            // Get updated contact
            val updatedDoc = firestore.collection(CONTACTS_COLLECTION)
                .document(contactDocId)
                .get()
                .await()

            val firebaseContact = updatedDoc.toObject(FirebaseContact::class.java)
                ?: throw Exception("Failed to get updated contact")

            val userProfile = getUserProfile(contactId)
            val contact = firebaseContact.toContact(userProfile)

            // Update local database
            contactDao.updateContact(contact)

            val response = com.tfkcolin.meena.data.models.ContactResponse(
                contact.id, contact.userId, contact.contactId, contact.displayName,
                contact.relationship, contact.notes, contact.createdAt, contact.updatedAt,
                contact.user
            )
            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteContact(contactId: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            val contactDocId = "${currentUserId}_${contactId}"

            // Delete from Firestore
            firestore.collection(CONTACTS_COLLECTION)
                .document(contactDocId)
                .delete()
                .await()

            // Delete from local database
            contactDao.deleteContact(contactDocId)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun blockContact(contactId: String): Result<com.tfkcolin.meena.data.models.ContactResponse> {
        return updateContact(contactId, null, "blocked", null)
    }

    override suspend fun unblockContact(contactId: String): Result<com.tfkcolin.meena.data.models.ContactResponse> {
        return updateContact(contactId, null, "friend", null)
    }

    override suspend fun searchContacts(query: String): Result<List<Contact>> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            if (query.isBlank()) {
                return Result.success(emptyList())
            }

            // Search contacts by display name
            val querySnapshot = firestore.collection(CONTACTS_COLLECTION)
                .whereEqualTo("userId", currentUserId)
                .whereGreaterThanOrEqualTo("displayName", query)
                .whereLessThanOrEqualTo("displayName", query + "\uf8ff")
                .limit(20)
                .get()
                .await()

            val firebaseContacts = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseContact::class.java)
            }

            val contacts = firebaseContacts.map { firebaseContact ->
                val userProfile = getUserProfile(firebaseContact.contactId)
                firebaseContact.toContact(userProfile)
            }

            Result.success(contacts)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private suspend fun getUserProfile(userId: String): UserProfile? {
        return try {
            val userDoc = firestore.collection(USERS_COLLECTION)
                .document(userId)
                .get()
                .await()

            if (userDoc.exists()) {
                val firebaseUser = userDoc.toObject(com.tfkcolin.meena.data.models.firebase.FirebaseUserProfile::class.java)
                firebaseUser?.let {
                    UserProfile(
                        id = it.userId,
                        userHandle = it.userHandle,
                        displayName = it.displayName,
                        bio = it.bio,
                        avatarUrl = it.avatarUrl,
                        verificationStatus = it.verificationStatus,
                        isVerified = it.verificationStatus == "verified",
                        isGoldMember = it.subscriptionTier == "gold",
                        lastActive = it.lastActive.toString(),
                        createdAt = it.createdAt.toString(),
                        followerCount = it.followerCount,
                        followingCount = it.followingCount
                    )
                }
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
}

/**
 * Extension function to convert FirebaseContact to Contact.
 */
private fun FirebaseContact.toContact(userProfile: UserProfile?): Contact {
    return Contact(
        id = this.id,
        userId = this.userId,
        contactId = this.contactId,
        displayName = this.displayName,
        relationship = this.relationship,
        notes = this.notes,
        createdAt = this.createdAt.toString(),
        updatedAt = this.updatedAt.toString(),
        isFavorite = this.isFavorite,
        lastInteractionAt = this.lastInteractionAt?.toString(),
        user = userProfile
    )
}
